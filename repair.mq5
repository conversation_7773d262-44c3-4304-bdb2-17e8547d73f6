//+------------------------------------------------------------------+
//|                                                       repair.mq5 |
//|                        Copyright 2024, MetaQuotes Software Corp. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input group "CETP-Plus Settings"
input int cetp_window = 5;                    // CETP Window (3-20)
input int cetp_bins = 3;                      // CETP Bins per Dimension (1-10)
input double long_threshold = 0.1;           // Long Threshold (0.1-2.0)
input double short_threshold = -0.1;         // Short Threshold (-0.8 to -0.01)
input double cetp_k = 0.8;                   // CETP Momentum Weight (0.1-5.0)
input double mom_scale = 6.0;                // Momentum Scale (1.0-20.0)
input double body_weight = 1.0;              // Body Ratio Weight (0.0-2.0)
input double upper_weight = 0.8;             // Upper Wick Ratio Weight (0.0-2.0)
input double lower_weight = 1.2;             // Lower Wick Ratio Weight (0.0-2.0)
input double decay_factor = 0.9;             // Decay Factor (0.1-0.99)

input group "Trade Settings"
input double min_score_strength = 0.03;      // Min CETP Score Strength (0.0-5.0)
input double stop_loss_pct = 0.5;            // Stop Loss (%) (0.1-5.0)
input double atr_mult = 3.0;                 // ATR Multiplier (0.5-5.0)
input double trail_mult = 5.0;               // Trailing ATR Mult (0.5-7.0)
input double trail_offset_pct = 1.0;         // Trail Start Offset (%) (0.5-2.0)
input double min_price_move_mult = 4.0;      // Min Price Move ATR Mult (0.5-5.0)
input double min_vol_mult = 1.5;             // Min Volume Multiplier (1.0+)
input double vol_threshold_limit = 12.0;     // Vol Threshold Limit (5.0-20.0)

//--- Global variables
double body_arr[];
double upper_arr[];
double lower_arr[];
int hist[];
double entry_price = 0.0;
int bars_held = 0;

//--- Indicator handles
int atr_handle;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   //--- Initialize arrays
   ArrayResize(body_arr, cetp_window);
   ArrayResize(upper_arr, cetp_window);
   ArrayResize(lower_arr, cetp_window);
   ArrayResize(hist, cetp_bins * cetp_bins * cetp_bins);

   //--- Initialize indicator handles
   atr_handle = iATR(_Symbol, PERIOD_CURRENT, 14);
   if(atr_handle == INVALID_HANDLE)
   {
      Print("Failed to create ATR indicator handle");
      return INIT_FAILED;
   }

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   //--- Release indicator handles
   if(atr_handle != INVALID_HANDLE)
      IndicatorRelease(atr_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   //--- Check if new bar
   static datetime last_bar_time = 0;
   datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(current_bar_time == last_bar_time)
      return;
   last_bar_time = current_bar_time;

   //--- Get current market data
   MqlRates rates[];
   if(CopyRates(_Symbol, PERIOD_CURRENT, 0, cetp_window + 20, rates) < cetp_window + 20)
      return;

   //--- Get ATR values
   double atr_values[];
   if(CopyBuffer(atr_handle, 0, 0, 20, atr_values) < 20)
      return;

   double atr = atr_values[0];

   //--- Calculate indicators
   CalculateIndicators(rates, atr);

// CETP-Plus Calculation
epsilon = 1e-5
body_ratio = (close - open) / (high - low + epsilon) * body_weight
upper_wick_ratio = (high - math.max(open, close)) / (high - low + epsilon) * upper_weight
lower_wick_ratio = (math.min(open, close) - low) / (high - low + epsilon) * lower_weight

// EMA-like weighting
var float[] body_arr = array.new_float(cetp_window, 0.0)
var float[] upper_arr = array.new_float(cetp_window, 0.0)
var float[] lower_arr = array.new_float(cetp_window, 0.0)
for i = 0 to cetp_window - 2
    array.set(body_arr, i, array.get(body_arr, i + 1) * decay_factor)
    array.set(upper_arr, i, array.get(upper_arr, i + 1) * decay_factor)
    array.set(lower_arr, i, array.get(lower_arr, i + 1) * decay_factor)
array.set(body_arr, cetp_window - 1, body_ratio)
array.set(upper_arr, cetp_window - 1, upper_wick_ratio)
array.set(lower_arr, cetp_window - 1, lower_wick_ratio)

// Volatility scaling (ATR thesis)
bin_size = 2.0 / cetp_bins * (1 + atr / ta.sma(atr, 14))
hist_size = cetp_bins * cetp_bins * cetp_bins
var int[] hist = array.new_int(hist_size, 0)
array.fill(hist, 0)
for i = 0 to cetp_window - 1
    body_val = array.get(body_arr, i)
    upper_val = array.get(upper_arr, i)
    lower_val = array.get(lower_arr, i)
    body_bin = math.max(0, math.min(cetp_bins - 1, math.floor((body_val + 1) / bin_size)))
    upper_bin = math.max(0, math.min(cetp_bins - 1, math.floor((upper_val + 1) / bin_size)))
    lower_bin = math.max(0, math.min(cetp_bins - 1, math.floor((lower_val + 1) / bin_size)))
    bin_idx = body_bin * (cetp_bins * cetp_bins) + upper_bin * cetp_bins + lower_bin
    array.set(hist, bin_idx, array.get(hist, bin_idx) + 1)

entropy = 0.0
for i = 0 to hist_size - 1
    count = array.get(hist, i)
    p = count / cetp_window
    if p > 0
        entropy := entropy - p * math.log(p)

max_entropy = math.log(hist_size)
norm_entropy = max_entropy > 0 ? entropy / max_entropy : 0.0

// RSI-like momentum bias (continued)
momentum = ta.mom(close, cetp_window) / (close[cetp_window] != 0 ? close[cetp_window] : 1e-5)
momentum_adj = momentum * (1 + rsi_bias)

// ADX-like trend strength
di_plus = ta.rma(math.max(high - high[1], 0), cetp_window) / atr
di_minus = ta.rma(math.max(low[1] - low, 0), cetp_window) / atr
trend_strength = di_plus > di_minus ? 1.2 : (di_minus > di_plus ? 1.2 : 1.0)

// CETP-Plus Score
avg_body = nz(array.avg(body_arr), 0.0)
raw_score = avg_body * (1 - norm_entropy) * (cetp_k + momentum_adj * mom_scale)
cetp_score = nz(raw_score * trend_strength, 0.0)

// Set conditions that depend on cetp_score
trade_allowed := cetp_score > long_threshold ? trade_allowed_long : trade_allowed_short
price_move_condition := cetp_score > long_threshold ? (high - low) > min_price_move_long : (high - low) > min_price_move_short
vol_condition := cetp_score > long_threshold ? vol_condition_long : vol_condition_short
momentum_condition_long = math.abs(momentum_adj) > 0.05
momentum_condition_short = math.abs(momentum_adj) > 0.03
momentum_condition = cetp_score > long_threshold ? momentum_condition_long : momentum_condition_short

// Position State
var float entry_price = na
var int bars_held = 0

if barstate.isconfirmed
    bars_held := strategy.position_size != 0 ? bars_held + 1 : 0

// Trade Logic
if bar_index >= cetp_window and math.abs(cetp_score) > min_score_strength and trade_allowed and price_move_condition and momentum_condition and vol_condition
    if cetp_score > long_threshold
        if strategy.position_size < 0
            strategy.close("Short", comment="Reverse to Long")
        if strategy.position_size <= 0
            strategy.entry("Long", strategy.long)
            entry_price := close
            sl = entry_price * (1 - stop_loss_pct / 100) - atr * atr_mult
            strategy.exit("Long Exit", "Long", stop=sl, trail_points=atr * trail_mult * syminfo.pointvalue, trail_offset=entry_price * (trail_offset_pct / 100))
            bars_held := 1
    else if cetp_score < short_threshold
        if strategy.position_size > 0
            strategy.close("Long", comment="Reverse to Short")
        if strategy.position_size >= 0
            strategy.entry("Short", strategy.short)
            entry_price := close
            sl = entry_price * (1 + stop_loss_pct / 100) + atr * atr_mult
            strategy.exit("Short Exit", "Short", stop=sl, trail_points=atr * trail_mult * syminfo.pointvalue, trail_offset=entry_price * (trail_offset_pct / 100))
            bars_held := 1